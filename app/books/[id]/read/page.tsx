"use client"

import React, { useState, useEffect, useRef, useCallback } from "react"
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { SmartTypography } from "@/components/SmartTypography"
import { checkBookReadAccess } from "@/lib/utils/book-access"
import { BookAudioDiscussion } from "@/components/BookAudioDiscussion"
import { Mic, ArrowLeft } from "lucide-react"


interface Book {
  id: string
  title: string
  cover_image_url: string
  user_id: string
  ebook_file_type: 'epub' | 'docx' | 'doc' | 'rtf'
  users: {
    name: string
    avatar: string
  }
}

interface Chapter {
  id: string
  title: string
  content: string
  chapter_number: number
  word_count: number
  is_published: boolean
}

interface ReadingProgress {
  chapter_id: string
  progress_percentage: number
  last_read_at: string
}

export default function BookReadPage() {
  const params = useParams()
  const router = useRouter()
  const [book, setBook] = useState<Book | null>(null)
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [currentChapter, setCurrentChapter] = useState<Chapter | null>(null)
  const [user, setUser] = useState<any>(null)
  const [hasPurchased, setHasPurchased] = useState(false)
  const [readingProgress, setReadingProgress] = useState<ReadingProgress[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [zenMode, setZenMode] = useState(false)
  const [fontSize, setFontSize] = useState(16)
  const [showChapterList, setShowChapterList] = useState(false)
  const [showFontSizeMenu, setShowFontSizeMenu] = useState(false)
  const [currentProgress, setCurrentProgress] = useState(0)
  const [showAudioDiscussion, setShowAudioDiscussion] = useState(false)
  const [audioDiscussionCounts, setAudioDiscussionCounts] = useState<Record<string, number>>({})
  const supabase = createSupabaseClient()

  // Track reading progress on scroll
  useEffect(() => {
    const handleScroll = () => {
      if (!currentChapter) return

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const scrollHeight = document.documentElement.scrollHeight - window.innerHeight
      const scrollPercentage = scrollHeight > 0 ? (scrollTop / scrollHeight) * 100 : 0
      const clampedProgress = Math.min(Math.max(scrollPercentage, 0), 100)

      setCurrentProgress(clampedProgress)
      updateReadingProgress(currentChapter.id, clampedProgress)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [currentChapter])

  // Reset progress when chapter changes
  useEffect(() => {
    if (currentChapter) {
      const savedProgress = getChapterProgress(currentChapter.id)
      setCurrentProgress(savedProgress)
    }
  }, [currentChapter])

  useEffect(() => {
    if (params.id) {
      checkAccess()
    }
  }, [params.id])

  // Close font size dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.font-size-dropdown')) {
        setShowFontSizeMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const checkAccess = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)

      if (!user) {
        // Instead of redirecting to login, show a sign-in prompt
        setError('Please sign in to read this book')
        setLoading(false)
        return
      }

      // Check access using unified access checking
      const accessResult = await checkBookReadAccess(supabase, user.id, params.id)

      if (!accessResult.hasAccess || !accessResult.book) {
        router.push(`/books/${params.id}`)
        return
      }

      // Get full book data with user info
      const { data: bookData, error: bookError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          cover_image_url,
          price_amount,
          user_id,
          ebook_file_type,
          users(name, avatar)
        `)
        .eq('id', params.id)
        .eq('is_ebook', true)
        .single()

      console.log('Book data query result:', { bookData, bookError })
      if (bookError) throw bookError
      setBook(bookData)
      setHasPurchased(true) // User has access if we reach this point

      // Fetch chapters
      console.log('Fetching chapters for project:', params.id)
      const { data: chaptersData, error: chaptersError } = await supabase
        .from('chapters')
        .select('id, title, content, chapter_number, word_count, is_published')
        .eq('project_id', params.id)
        .eq('is_published', true)
        .order('chapter_number')

      console.log('Chapters query result:', { chaptersData, chaptersError })
      setChapters(chaptersData || [])

      // Fetch reading progress
      const { data: progressData } = await supabase
        .from('reading_progress')
        .select('chapter_id, progress_percentage, last_read_at')
        .eq('user_id', user.id)
        .eq('project_id', params.id)

      setReadingProgress(progressData || [])

      // Set current chapter (last read or first chapter)
      if (chaptersData && chaptersData.length > 0) {
        const lastReadProgress = progressData?.sort((a, b) => 
          new Date(b.last_read_at).getTime() - new Date(a.last_read_at).getTime()
        )[0]

        if (lastReadProgress) {
          const lastChapter = chaptersData.find(c => c.id === lastReadProgress.chapter_id)
          setCurrentChapter(lastChapter || chaptersData[0])
        } else {
          setCurrentChapter(chaptersData[0])
        }
      }

    } catch (error) {
      console.error('Error checking access:', error)
      console.error('Error details:', {
        message: error?.message,
        code: error?.code,
        details: error?.details,
        hint: error?.hint,
        stack: error?.stack
      })
      console.error('Full error object:', JSON.stringify(error, null, 2))
      // Don't redirect immediately on error - user might have access
      setLoading(false)
    } finally {
      setLoading(false)
    }
  }

  const updateReadingProgress = async (chapterId: string, percentage: number) => {
    if (!user) return

    try {
      await supabase
        .from('reading_progress')
        .upsert({
          user_id: user.id,
          project_id: params.id,
          chapter_id: chapterId,
          progress_percentage: percentage,
          last_read_at: new Date().toISOString()
        })

      // Update local state
      setReadingProgress(prev => {
        const existing = prev.find(p => p.chapter_id === chapterId)
        if (existing) {
          return prev.map(p => 
            p.chapter_id === chapterId 
              ? { ...p, progress_percentage: percentage, last_read_at: new Date().toISOString() }
              : p
          )
        } else {
          return [...prev, {
            chapter_id: chapterId,
            progress_percentage: percentage,
            last_read_at: new Date().toISOString()
          }]
        }
      })
    } catch (error) {
      console.error('Error updating reading progress:', error)
    }
  }

  const navigateChapter = (direction: 'prev' | 'next') => {
    if (!currentChapter) return

    const currentIndex = chapters.findIndex(c => c.id === currentChapter.id)
    let newIndex = direction === 'next' ? currentIndex + 1 : currentIndex - 1

    if (newIndex >= 0 && newIndex < chapters.length) {
      setCurrentChapter(chapters[newIndex])
      updateReadingProgress(chapters[newIndex].id, 0)

      // Scroll to top of the new chapter
      setTimeout(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' })
      }, 100)
    }
  }

  const getChapterProgress = (chapterId: string) => {
    return readingProgress.find(p => p.chapter_id === chapterId)?.progress_percentage || 0
  }

  // Load audio discussion counts for all chapters
  const loadAudioDiscussionCounts = useCallback(async () => {
    if (!book?.id || chapters.length === 0) return

    try {
      const { data, error } = await supabase
        .from('book_audio_posts')
        .select('chapter_id')
        .eq('project_id', book.id)

      if (error) throw error

      // Count discussions per chapter
      const counts: Record<string, number> = {}
      data?.forEach(discussion => {
        counts[discussion.chapter_id] = (counts[discussion.chapter_id] || 0) + 1
      })

      setAudioDiscussionCounts(counts)
    } catch (error) {
      console.error('Error loading audio discussion counts:', error)
    }
  }, [book?.id, chapters.length, supabase])

  // Format count for display (e.g., 1234 -> 1.2k)
  const formatCount = (count: number): string => {
    if (count < 1000) return count.toString()
    if (count < 1000000) return (count / 1000).toFixed(1).replace(/\.0$/, '') + 'k'
    return (count / 1000000).toFixed(1).replace(/\.0$/, '') + 'M'
  }

  // Load audio discussion counts when book and chapters are available
  useEffect(() => {
    if (book && chapters.length > 0) {
      loadAudioDiscussionCounts()
    }
  }, [book, chapters.length, loadAudioDiscussionCounts])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 animate-pulse">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  // Show sign-in prompt for unauthenticated users
  if (error && error.includes('sign in')) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8">
          <div className="text-6xl mb-6">📖</div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">Sign in to read this book</h2>
          <p className="text-gray-600 mb-8">
            Create an account or sign in to access this book and start reading.
          </p>
          <div className="space-y-3">
            <Link href="/auth/login" className="w-full">
              <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                Sign In
              </Button>
            </Link>
            <Link href="/auth/signup" className="w-full">
              <Button variant="outline" className="w-full">
                Create Account
              </Button>
            </Link>
            <Link href={`/books/${params.id}`} className="w-full">
              <Button variant="ghost" className="w-full text-gray-600">
                ← Back to Book Details
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  if (!book || !currentChapter) {
    console.log('Debug - book:', book)
    console.log('Debug - currentChapter:', currentChapter)
    console.log('Debug - chapters:', chapters)
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">📚</div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">Book not available</h2>
          <p className="text-gray-600 mb-4">
            Book: {book ? 'Found' : 'Missing'}, Chapter: {currentChapter ? 'Found' : 'Missing'}
          </p>
          <Link href="/books">
            <Button>Browse Books</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className={`min-h-screen transition-colors duration-500 ${zenMode ? 'bg-slate-900' : 'bg-gray-50'}`}>
      
      {/* Reading Header */}
      <div className={`sticky top-0 z-50 transition-all duration-500 ${
        zenMode ? 'bg-slate-800/90 backdrop-blur-xl border-b border-slate-700' : 'bg-white/90 backdrop-blur-sm border-b border-gray-200'
      }`}>
        <div className="max-w-4xl mx-auto px-3 sm:px-4 py-2 sm:py-3">
          <div className="flex items-center justify-between">

            {/* Back Button and Book Info */}
            <div className="flex items-center gap-2 sm:gap-3">
              {/* Back Button */}
              <Link href={`/books/${book.id}`}>
                <Button
                  variant="ghost"
                  size="sm"
                  className={`flex items-center gap-1 px-2 sm:px-3 ${
                    zenMode
                      ? 'text-white hover:bg-slate-700 hover:text-white'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                  title="Back to book page"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span className="hidden sm:inline">Back</span>
                </Button>
              </Link>

              <div className="block">
                <h1 className={`font-medium text-sm sm:text-base ${
                  zenMode ? 'text-white' : 'text-gray-900'
                }`}
                style={{
                  maxWidth: '200px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
                title={book.title}
                >
                  {book.title}
                </h1>
                <p className={`text-xs ${
                  zenMode ? 'text-white/50' : 'text-gray-500'
                }`}>
                  by {book.users.name}
                </p>
              </div>
            </div>

            {/* Reading Controls */}
            <div className="flex items-center gap-1 sm:gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.location.href = '/library'}
                className={`text-xs sm:text-sm px-2 sm:px-3 bg-white/5 dark:bg-white/5 border border-white/10 dark:border-white/10 rounded-lg shadow-sm dark:shadow-black/60 hover:bg-purple-100 dark:hover:bg-white/8 dark:hover:border-white/20 transition-all duration-200 ${zenMode ? 'text-white' : ''}`}
                title="Go to Library"
              >
                📚
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowChapterList(!showChapterList)}
                className={`text-xs sm:text-sm px-2 sm:px-3 bg-white/5 dark:bg-white/5 border border-white/10 dark:border-white/10 rounded-lg shadow-sm dark:shadow-black/60 hover:bg-purple-100 dark:hover:bg-white/8 dark:hover:border-white/20 transition-all duration-200 ${zenMode ? 'text-white' : ''}`}
                title="Chapter List"
              >
                📋
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  console.log('Audio discussion clicked - mic should be purple!')
                  setShowAudioDiscussion(true)
                }}
                className={`relative text-xs sm:text-sm px-2 sm:px-3 bg-white/5 dark:bg-white/5 border border-white/10 dark:border-white/10 rounded-lg shadow-sm dark:shadow-black/60 hover:bg-purple-100 dark:hover:bg-white/8 dark:hover:border-white/20 transition-all duration-200 ${zenMode ? 'text-white' : ''}`}
                title="Audio Discussions"
              >
                <Mic
                  className="h-4 w-4"
                  style={{
                    color: '#a855f7 !important',
                    fill: '#a855f7 !important',
                    stroke: '#a855f7 !important',
                    filter: 'brightness(1.3) saturate(1.2) !important'
                  }}
                />
                {/* Dynamic counter - only show when there are discussions */}
                {currentChapter && audioDiscussionCounts[currentChapter.id] > 0 && (
                  <span className="absolute bottom-0 right-0 text-white text-[8px] font-bold leading-none z-10 bg-purple-600 dark:bg-purple-500 rounded-full min-w-[12px] h-3 flex items-center justify-center px-0.5">
                    {formatCount(audioDiscussionCounts[currentChapter.id])}
                  </span>
                )}
              </Button>
              {/* Font Size Dropdown */}
              <div className="relative font-size-dropdown">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFontSizeMenu(!showFontSizeMenu)}
                  className={`text-xs sm:text-sm px-2 sm:px-3 bg-white/5 dark:bg-white/5 border border-white/10 dark:border-white/10 rounded-lg shadow-sm dark:shadow-black/60 hover:bg-purple-100 dark:hover:bg-white/8 dark:hover:border-white/20 transition-all duration-200 ${zenMode ? 'text-white' : ''}`}
                  title="Font Size"
                >
                  A
                </Button>
                {showFontSizeMenu && (
                  <div className={`absolute bottom-full right-0 mb-2 border rounded-lg shadow-lg p-3 z-50 ${
                    zenMode ? 'bg-slate-800 border-slate-700' : 'bg-white border-gray-200'
                  }`}>
                    <div className="flex flex-col space-y-2 min-w-[120px]">
                      <div className={`text-xs font-medium text-center ${
                        zenMode ? 'text-white/70' : 'text-gray-600'
                      }`}>Font Size</div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setFontSize(prev => Math.max(prev - 2, 12))}
                          className={`px-2 py-1 text-xs ${zenMode ? 'text-white hover:bg-slate-700' : ''}`}
                          disabled={fontSize <= 12}
                        >
                          A-
                        </Button>
                        <span className={`text-xs font-medium min-w-[30px] text-center ${
                          zenMode ? 'text-white' : 'text-gray-900'
                        }`}>{fontSize}px</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setFontSize(prev => Math.min(prev + 2, 24))}
                          className={`px-2 py-1 text-xs ${zenMode ? 'text-white hover:bg-slate-700' : ''}`}
                          disabled={fontSize >= 24}
                        >
                          A+
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setZenMode(!zenMode)}
                className={`bg-white/5 dark:bg-white/5 border border-white/10 dark:border-white/10 rounded-lg shadow-sm dark:shadow-black/60 hover:bg-purple-100 dark:hover:bg-white/8 dark:hover:border-white/20 transition-all duration-200 ${zenMode ? 'text-white' : ''}`}
                title={zenMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
              >
                {zenMode ? '☀️' : '🌙'}
              </Button>
            </div>
          </div>
        </div>

        {/* Word Counter Bar */}
        <div className={`border-t ${zenMode ? 'border-slate-700 bg-slate-800' : 'border-gray-200 bg-gray-50'}`}>
          <div className="max-w-4xl mx-auto px-3 sm:px-4 py-2">
            <div className={`h-0.5 rounded ${zenMode ? 'bg-slate-700' : 'bg-gray-300'}`}>
              <div
                className={`h-full rounded transition-all duration-300 ${zenMode ? 'bg-purple-400' : 'bg-purple-500'}`}
                style={{ width: `${currentProgress}%` }}
              />
            </div>
            <div className="flex justify-between items-center text-xs mt-1">
              <span className={zenMode ? 'text-slate-400' : 'text-gray-500'}>
                Chapter {currentChapter.chapter_number}
              </span>
              <span className={`font-medium ${zenMode ? 'text-purple-400' : 'text-purple-600'}`}>
                {Math.max(0, Math.floor(currentChapter.word_count * (1 - currentProgress / 100))).toLocaleString()} words remaining
              </span>
            </div>
          </div>
        </div>
      </div>



      {/* Chapter List Sidebar */}
      {showChapterList && (
        <>
          {/* Backdrop for mobile */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
            onClick={() => setShowChapterList(false)}
          />

          <div className={`fixed inset-y-0 left-0 w-full sm:w-80 max-w-sm z-40 transform transition-transform duration-300 ${
            zenMode ? 'bg-slate-800 border-r border-slate-700' : 'bg-white border-r border-gray-200'
          }`}>
            <div className="p-4 h-full overflow-y-auto">
              {/* Close button for mobile */}
              <div className="flex items-center justify-between mb-4 lg:hidden">
                <h3 className={`font-semibold ${zenMode ? 'text-white' : 'text-gray-900'}`}>
                  Chapters
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowChapterList(false)}
                  className={`bg-white/5 dark:bg-white/5 border border-white/10 dark:border-white/10 rounded-lg shadow-sm dark:shadow-black/60 hover:bg-purple-100 dark:hover:bg-white/8 dark:hover:border-white/20 transition-all duration-200 ${zenMode ? 'text-white' : ''}`}
                >
                  ✕
                </Button>
              </div>

              <h3 className={`font-semibold mb-4 hidden lg:block ${zenMode ? 'text-white' : 'text-gray-900'}`}>
                Chapters
              </h3>

              <div className="space-y-2">
              {chapters.map((chapter) => (
                <button
                  key={chapter.id}
                  onClick={() => {
                    setCurrentChapter(chapter)
                    setShowChapterList(false)
                    updateReadingProgress(chapter.id, 0)

                    // Scroll to top when selecting chapter from list
                    setTimeout(() => {
                      window.scrollTo({ top: 0, behavior: 'smooth' })
                    }, 100)
                  }}
                  className={`w-full text-left p-3 rounded-lg transition-colors ${
                    currentChapter.id === chapter.id
                      ? zenMode ? 'bg-slate-700 text-white' : 'bg-purple-50 text-purple-900'
                      : zenMode ? 'hover:bg-slate-700 text-white/70' : 'hover:bg-gray-50 text-gray-700'
                  }`}
                >
                  <div className="font-medium text-sm">
                    Chapter {chapter.chapter_number}
                  </div>
                  <div className="text-xs opacity-75 truncate">
                    {chapter.title}
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    <div className={`h-1 bg-gray-300 rounded-full flex-1 ${
                      zenMode ? 'bg-slate-600' : 'bg-gray-200'
                    }`}>
                      <div 
                        className="h-1 bg-purple-500 rounded-full transition-all"
                        style={{ width: `${getChapterProgress(chapter.id)}%` }}
                      />
                    </div>
                    <span className="text-xs">
                      {Math.round(getChapterProgress(chapter.id))}%
                    </span>
                  </div>
                </button>
              ))}
              </div>
            </div>
          </div>
        </>
      )}



      {/* Main Reading Area */}
      <div className={`transition-all duration-300 ${showChapterList ? 'lg:ml-80' : 'ml-0'}`}>
        <div className="max-w-4xl mx-auto px-3 sm:px-4 py-6 sm:py-8">

          {/* Chapter Header */}
          <div className={`text-center mb-6 sm:mb-8 ${zenMode ? 'text-white' : 'text-gray-900'}`}>
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-serif mb-3 sm:mb-4">
              {currentChapter.title}
            </h1>
            <div className={`text-xs sm:text-sm ${zenMode ? 'text-white/50' : 'text-gray-500'}`}>
              {currentChapter.word_count.toLocaleString()} words • {Math.ceil(currentChapter.word_count / 200)} min read
            </div>
          </div>



          {/* Chapter Content */}
          <div
            className="flex justify-center"
            style={{ fontSize: `${fontSize}px` }}
          >
            <SmartTypography
              content={currentChapter.content}
              zenMode={zenMode}
              isPreview={false}
              isDesktop={true}
              enableAdvancedFeatures={true}
              className="max-w-none"
            />
          </div>

          {/* Chapter Navigation */}
          <div className={`flex justify-between items-center mt-12 pt-8 border-t ${
            zenMode ? 'border-slate-600' : 'border-gray-200'
          }`}>
            <Button
              variant="outline"
              onClick={() => navigateChapter('prev')}
              disabled={chapters.findIndex(c => c.id === currentChapter.id) === 0}
              className={zenMode ? 'border-slate-600 text-white hover:bg-slate-700 hover:text-white' : ''}
              style={zenMode ? {
                color: '#ffffff !important',
                borderColor: '#475569 !important',
                backgroundColor: 'transparent !important'
              } : {}}
            >
              ← Previous Chapter
            </Button>

            <div className={`text-sm ${zenMode ? 'text-white/50' : 'text-gray-500'}`}>
              {chapters.findIndex(c => c.id === currentChapter.id) + 1} of {chapters.length}
            </div>

            <Button
              variant="outline"
              onClick={() => navigateChapter('next')}
              disabled={chapters.findIndex(c => c.id === currentChapter.id) === chapters.length - 1}
              className={zenMode ? 'border-slate-600 text-white hover:bg-slate-700 hover:text-white' : ''}
              style={zenMode ? {
                color: '#ffffff !important',
                borderColor: '#475569 !important',
                backgroundColor: 'transparent !important'
              } : {}}
            >
              Next Chapter →
            </Button>
          </div>
        </div>
      </div>

      {/* Book Audio Discussion Modal */}
      {showAudioDiscussion && currentChapter && book && (
        <BookAudioDiscussion
          bookId={book.id}
          chapterId={currentChapter.id}
          chapterTitle={currentChapter.title}
          currentUserId={user?.id}
          onClose={() => setShowAudioDiscussion(false)}
          onDiscussionAdded={loadAudioDiscussionCounts}
        />
      )}
    </div>
  )
}
