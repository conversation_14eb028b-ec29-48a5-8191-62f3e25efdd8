"use client"

import { useState } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ShareButton } from "@/components/ShareButton"

interface Book {
  id: string
  title: string
  description: string
  cover_image_url: string
  price_amount: number
  genre: string
  book_type?: string
  average_rating?: number
  review_count?: number
  sales_count?: number
  created_at: string
  slug?: string
  author_name?: string
  users: {
    name: string
    avatar?: string
    profile_picture_url?: string
  }
}

interface BookCardProps {
  book: Book
  showShareButton?: boolean
  priority?: boolean
}

export function BookCard({ book, showShareButton = true, priority = false }: BookCardProps) {
  const [imageError, setImageError] = useState(false)

  const formatPrice = (cents: number | null) => {
    if (cents === 0 || cents === null) return "Free"
    return `$${(cents / 100).toFixed(2)}`
  }

  const renderPenRating = (rating: number = 0, reviewCount: number = 0) => {
    if (reviewCount === 0 || !rating) {
      return (
        <div className="flex items-center text-xs text-gray-400 truncate">
          <span>No ratings yet</span>
        </div>
      )
    }

    const fullPens = Math.floor(rating)
    const hasHalfPen = rating % 1 >= 0.5

    return (
      <div className="flex items-center gap-1 min-w-0">
        <div className="flex flex-shrink-0">
          {[...Array(5)].map((_, i) => (
            <span
              key={i}
              className={`text-[10px] ${
                i < fullPens
                  ? 'text-purple-600'
                  : i === fullPens && hasHalfPen
                    ? 'text-purple-400'
                    : 'text-gray-300'
              }`}
            >
              🖊️
            </span>
          ))}
        </div>
        <span className="text-xs text-gray-600 truncate">
          {rating.toFixed(1)} ({reviewCount})
        </span>
      </div>
    )
  }

  const getBookUrl = () => {
    return book.slug ? `/books/${book.slug}` : `/books/${book.id}`
  }



  const getBadges = () => {
    const badges = []
    
    // New release badge (within 30 days)
    const daysSinceRelease = Math.floor((Date.now() - new Date(book.created_at).getTime()) / (1000 * 60 * 60 * 24))
    if (daysSinceRelease <= 30) {
      badges.push({ text: 'New', color: 'bg-green-500' })
    }
    
    // Bestseller badge (more than 50 sales)
    if ((book.sales_count || 0) > 50) {
      badges.push({ text: 'Bestseller', color: 'bg-purple-500' })
    }

    // Highly rated badge (4.5+ rating with 10+ reviews)
    if ((book.average_rating || 0) >= 4.5 && (book.review_count || 0) >= 10) {
      badges.push({ text: 'Highly Rated', color: 'bg-yellow-500' })
    }
    
    // Free badge
    if (book.price_amount === 0) {
      badges.push({ text: 'Free', color: 'bg-blue-500' })
    }
    
    return badges
  }

  const badges = getBadges()

  return (
    <Card className="group hover:shadow-xl transition-all duration-500 hover:scale-[1.02] cursor-pointer h-full relative border-gray-100 hover:border-purple-200 bg-white hover:bg-gradient-to-br hover:from-white hover:to-purple-50/20">

      {/* Share Button */}
      {showShareButton && (
        <div className="absolute top-2 left-2 z-10">
          <ShareButton
            title={book.title}
            writerName={book.author_name || book.users.name}
            contentType="book"
            contentId={book.id}
            variant="compact"
            url={`${typeof window !== 'undefined' ? window.location.origin : ''}${getBookUrl()}`}
            className="bg-white/90 backdrop-blur-sm border-gray-200 hover:bg-white text-gray-600 hover:text-gray-900"
          />
        </div>
      )}

      <Link href={getBookUrl()}>
        {/* Book Cover - Full visibility without cropping */}
        <div className="aspect-[3/4] relative overflow-hidden rounded-t-lg bg-white border border-gray-100">
          {book.cover_image_url && !imageError ? (
            <img
              src={book.cover_image_url}
              alt={book.title}
              className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-300 bg-gradient-to-br from-purple-50 to-blue-50"
              onError={() => setImageError(true)}
              loading={priority ? "eager" : "lazy"}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-4xl bg-gradient-to-br from-purple-100 to-blue-100">
              📖
            </div>
          )}

          {/* Badges - Price and Status */}
          <div className="absolute top-2 right-2 flex flex-col gap-1">
            {/* Price Badge - Always show */}
            <span className="bg-white/95 backdrop-blur-sm text-gray-900 text-xs px-2 py-1 rounded-full font-medium border border-gray-200">
              {formatPrice(book.price_amount)}
            </span>

            {/* Status Badges - Show non-price badges */}
            {badges.filter(badge => badge.text !== 'Free').slice(0, 1).map((badge, index) => (
              <span
                key={index}
                className={`${badge.color} text-white text-xs px-2 py-1 rounded-full font-medium`}
              >
                {badge.text}
              </span>
            ))}
          </div>


        </div>

        <CardContent className="p-3 h-[160px] flex flex-col bg-white">
          {/* Title - Smart single/double line display */}
          <div className="h-[32px] mb-2 flex items-start">
            <h3 className={`font-semibold text-gray-900 group-hover:text-purple-600 transition-colors text-xs overflow-hidden ${
              book.title.length > 35 ? 'line-clamp-2 leading-[16px]' : 'line-clamp-1 leading-[32px] flex items-center'
            }`}>
              {book.title}
            </h3>
          </div>

          {/* Author - Fixed height and position */}
          <div className="h-[18px] flex items-center gap-1.5 mb-2">
            {book.users.avatar_url ? (
              <img
                src={book.users.avatar_url}
                alt={book.users.name}
                className="w-3.5 h-3.5 rounded-full flex-shrink-0"
              />
            ) : (
              <div className="w-3.5 h-3.5 bg-gray-200 rounded-full flex items-center justify-center text-[10px] flex-shrink-0">
                👤
              </div>
            )}
            <span className="text-xs text-gray-600 truncate">
              by {book.author_name || book.users.name}
            </span>
            {book.users.has_day1_badge && (
              <Day1Badge
                signupNumber={book.users.signup_number}
                badgeTier={book.users.badge_tier}
                size="sm"
                className="flex-shrink-0"
              />
            )}
          </div>

          {/* Description - Fixed height, exactly 2 lines maximum */}
          <div className="h-[32px] mb-2 flex items-start">
            {book.description && (
              <p className="text-xs text-gray-600 line-clamp-2 leading-[16px] overflow-hidden">
                {book.description}
              </p>
            )}
          </div>

          {/* Genre - Only show main genre, skip "Other" */}
          <div className="h-[20px] mb-2 flex items-start">
            {book.genre && book.genre.toLowerCase() !== 'other' && (
              <span className="inline-block px-2 py-0.5 bg-purple-100 text-purple-700 text-[10px] rounded font-medium leading-none">
                {book.genre.replace('_', ' ')}
              </span>
            )}
          </div>

          {/* Dedicated Rating & Sales Section */}
          <div className="mt-auto">
            <div className="border-t border-gray-100 pt-2">
              <div className="flex items-center justify-between text-xs h-[16px]">
                <div className="flex items-center min-w-0 flex-1">
                  {renderPenRating(book.average_rating || 0, book.review_count || 0)}
                </div>
                {(book.sales_count || 0) > 0 && (
                  <div className="text-gray-500 font-medium ml-2 flex-shrink-0">
                    {book.sales_count} sales
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Link>
    </Card>
  )
}
