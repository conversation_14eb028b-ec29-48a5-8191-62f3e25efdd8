"use client"

import { useEffect, useRef, useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"

interface VideoPlayerProps {
  videoId: string
  r2PublicUrl: string
  title?: string
  hasAccess: boolean
  writerName: string
  writerId: string
  initialViewCount?: number
}

export function VideoPlayer({
  videoId,
  r2PublicUrl,
  title,
  hasAccess,
  writerName,
  writerId,
  initialViewCount = 0
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [viewCounted, setViewCounted] = useState(false)
  const [viewCount, setViewCount] = useState(initialViewCount)
  const supabase = createSupabaseClient()

  useEffect(() => {
    console.log("VideoPlayer props:", { videoId, r2PublicUrl, title, hasAccess, writerName, writerId });
    console.log("Video URL being used:", r2PublicUrl);

    // Test if video URL is accessible
    if (r2PublicUrl) {
      fetch(r2PublicUrl, { method: 'HEAD' })
        .then(response => {
          console.log('Video URL test:', response.status, response.statusText);
          console.log('Content-Type:', response.headers.get('content-type'));
          console.log('Content-Length:', response.headers.get('content-length'));
        })
        .catch(error => {
          console.error('Video URL not accessible:', error);
        });
    }
  }, [videoId, r2PublicUrl, title, hasAccess, writerName, writerId])

  // Track video views
  const handlePlay = async () => {
    if (!viewCounted && hasAccess) {
      try {
        // Increment view_count atomically
        // supabase.sql helper not typed, so cast and ignore TS
        // Get current view count and increment it
        const { data: currentVideo } = await supabase
          .from('videos')
          .select('view_count')
          .eq('id', videoId)
          .single()

        const newViewCount = (currentVideo?.view_count || 0) + 1

        await supabase
          .from('videos')
          .update({ view_count: newViewCount })
          .eq('id', videoId)

        setViewCount(newViewCount)
        setViewCounted(true)
      } catch (error) {
        console.error('Failed to track video view:', error)
      }
    }
  }

  if (!hasAccess && !timelineMode) {
    return (
      <div className="relative bg-black rounded-lg overflow-hidden" style={{ aspectRatio: '16/9' }}>
        {/* Blurred Preview */}
        <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="text-6xl mb-4">🎥</div>
            <h3 className="text-xl font-semibold mb-2">Video Content Locked</h3>
            <p className="text-gray-300 mb-4">Subscribe to watch this video</p>
            <button
              onClick={() => {
                // Trigger subscription (same as photo overlay)
                const subscribeButton = document.querySelector('[data-subscribe-button]') as HTMLButtonElement
                if (subscribeButton) {
                  subscribeButton.click()
                }
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Subscribe to {writerName}
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {title && (
        <h3 className="text-lg font-semibold text-gray-800 text-center">{title}</h3>
      )}

      <div className="flex justify-center">
        <div className="relative bg-black rounded-lg overflow-hidden video-container max-w-4xl w-full" style={{ aspectRatio: '16/9' }}>
          <video
            ref={videoRef}
            controls
            className="w-full h-full"
            preload="metadata"
            playsInline
            muted
            style={{
              aspectRatio: '16/9',
              backgroundColor: '#000'
            }}
            onPlay={handlePlay}
            onError={(e) => {
              console.error('Video playback error:', e);
              console.error('Video URL:', r2PublicUrl);
              console.error('Video element:', e.target);
            }}
            onLoadStart={() => console.log('Video load started for:', r2PublicUrl)}
            onCanPlay={() => console.log('Video can play:', r2PublicUrl)}
            onLoadedData={() => console.log('Video data loaded:', r2PublicUrl)}
          >
            <source src={r2PublicUrl} type="video/mp4" />
            Your browser doesn't support video playback.
          </video>

          {/* Professional Watermark - top right, doesn't interfere with controls */}
          <div
            className="absolute top-3 right-3 pointer-events-none select-none"
            style={{
              zIndex: 2147483647,
              opacity: 0.8
            }}
          >
            <div className="flex items-center gap-1.5 bg-black/40 backdrop-blur-sm rounded px-2 py-1 border border-white/15 shadow-lg">
              <div className="w-3 h-3 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
                <svg className="w-1.5 h-1.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 22 12 18.27 5.82 22 7 13.87 2 9l6.91-.74L12 2z" />
                </svg>
              </div>
              <span className="text-white font-medium text-xs tracking-wide">
                OnlyDiary.app
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Video Info Bar - Only show if there are views */}
      {viewCount > 0 && (
        <div className="flex items-center gap-4 text-sm mt-3">
          <div className="flex items-center gap-1 text-white bg-black/20 px-2 py-1 rounded-lg">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
            </svg>
            <span className="font-semibold">{viewCount.toLocaleString()}</span>
            <span>view{viewCount !== 1 ? 's' : ''}</span>
          </div>
          <span className="text-white/60">•</span>
          <span className="text-white/80">by {writerName}</span>
        </div>
      )}


    </div>
  )
}
