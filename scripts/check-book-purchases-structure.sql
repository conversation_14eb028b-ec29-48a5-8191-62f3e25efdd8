-- Check the actual book_purchases table structure
-- Run this in Supabase SQL Editor to see what columns exist

-- 1. Check the actual table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'book_purchases' 
    AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Check if there are any book_purchases records at all
SELECT COUNT(*) as total_book_purchases FROM book_purchases;

-- 3. Check recent book_purchases (without status column)
SELECT 
    bp.id,
    bp.user_id,
    bp.project_id,
    bp.purchase_price_cents,
    bp.purchased_at,
    p.title,
    p.sales_count,
    u.name as user_name
FROM book_purchases bp
JOIN projects p ON bp.project_id = p.id
LEFT JOIN users u ON bp.user_id = u.id
ORDER BY bp.purchased_at DESC
LIMIT 10;

-- 4. Check if the trigger exists and what it's looking for
SELECT 
    trigger_name,
    event_manipulation,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'book_purchases';

-- 5. Check the actual trigger function code
SELECT 
    routine_name,
    routine_definition
FROM information_schema.routines 
WHERE routine_name LIKE '%project_sales%' 
    OR routine_name LIKE '%update_project%';

-- 6. Compare sales_count vs actual purchases count
SELECT 
    p.id,
    p.title,
    p.sales_count,
    COUNT(bp.id) as actual_purchases,
    CASE 
        WHEN p.sales_count = COUNT(bp.id) THEN 'MATCH ✓'
        ELSE 'MISMATCH ✗'
    END as status
FROM projects p
LEFT JOIN book_purchases bp ON p.id = bp.project_id
WHERE p.is_ebook = true 
    AND p.is_complete = true
    AND (p.sales_count > 0 OR COUNT(bp.id) > 0)
GROUP BY p.id, p.title, p.sales_count
ORDER BY p.sales_count DESC;
