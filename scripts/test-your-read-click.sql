-- Test script to see if your recent "Read" click worked
-- Replace 'YOUR_EMAIL' with your actual email

-- 1. Find your user ID
SELECT id, name, email FROM users WHERE email ILIKE '%YOUR_EMAIL%' LIMIT 5;

-- 2. Check what books you have purchased
SELECT 
    bp.id,
    bp.project_id,
    bp.purchase_price_cents,
    bp.purchased_at,
    p.title
FROM book_purchases bp
JOIN projects p ON bp.project_id = p.id
WHERE bp.user_id = 'YOUR_USER_ID_HERE'  -- Replace with your actual user ID
ORDER BY bp.purchased_at DESC;

-- 3. Check what books are in your library
SELECT 
    ul.id,
    ul.project_id,
    ul.added_at,
    p.title
FROM user_library ul
JOIN projects p ON ul.project_id = p.id
WHERE ul.user_id = 'YOUR_USER_ID_HERE'  -- Replace with your actual user ID
ORDER BY ul.added_at DESC;

-- 4. Check the "RIP David Weaver" book specifically
SELECT 
    id,
    title,
    sales_count,
    price_amount,
    is_ebook,
    is_complete
FROM projects 
WHERE title ILIKE '%david weaver%' OR title ILIKE '%rip%';

-- 5. Check if you already "purchased" the <PERSON> book
SELECT 
    bp.id,
    bp.purchased_at,
    'Already purchased' as status
FROM book_purchases bp
JOIN projects p ON bp.project_id = p.id
WHERE bp.user_id = 'YOUR_USER_ID_HERE'  -- Replace with your actual user ID
    AND (p.title ILIKE '%david weaver%' OR p.title ILIKE '%rip%');
